<script lang="ts">
    import { goto } from '$app/navigation';
    import { onMount } from 'svelte';
    import { authStore } from '$lib/stores/auth';
    import PriceCard from '$lib/components/PriceCard.svelte';
    import PaymentProcessingModal from '$lib/components/PaymentProcessingModal.svelte';
    import { PRICING_PLANS } from '$lib/config/pricing';
    import { createCheckoutSession, checkSubscriptionStatus } from '$lib/services/subscriptionService';
    import type { User } from '@supabase/supabase-js';
	
    let loading = true;
    let user: User | null = null;
    let processingPayment = false;
    let checkingSubscription = false;

    // Modal state
    let showPaymentModal = false;
	
    let unsubscribe: (() => void) | null = null;
    let paymentMessageTimeout: ReturnType<typeof setTimeout> | null = null;
    
    function setPaymentMessage(message: string) {
        paymentMessage = message;
        
        // Clear any existing timeout
        if (paymentMessageTimeout) {
            clearTimeout(paymentMessageTimeout);
        }
        
        // Auto-clear message after 8 seconds
        paymentMessageTimeout = setTimeout(() => {
            paymentMessage = '';
            paymentMessageTimeout = null;
        }, 8000);
    }
    
    function clearPaymentMessage() {
        paymentMessage = '';
        if (paymentMessageTimeout) {
            clearTimeout(paymentMessageTimeout);
            paymentMessageTimeout = null;
        }
    }
    
    onMount(() => {
        
        // Check authentication status
        unsubscribe = authStore.subscribe(async (authState) => {
            if (!authState.initialized) return;
            
            if (!authState.session || !authState.user) {
                // User is not authenticated, redirect to signup
                goto('/auth/signup');
                return;
            }
            
            user = authState.user;
            loading = false;
        });
        
        // Clean up on component destroy
        return () => {
            if (unsubscribe) {
                unsubscribe();
            }
            if (paymentMessageTimeout) {
                clearTimeout(paymentMessageTimeout);
            }
        };
    });
    
    async function handleSubscriptionSelect(priceId: string) {
        if (!user || processingPayment) return;

        showPaymentModal = true;
        processingPayment = true;

        try {
            const result = await createCheckoutSession(priceId);

            if (result.error) {
                setPaymentMessage(result.error);
            } else if (result.url) {
                // Redirect to Stripe Checkout
                window.location.href = result.url;
            } else {
                setPaymentMessage('Unable to start checkout process. Please try again.');
            }
        } catch (error) {
            console.error('Error opening payment:', error);
            setPaymentMessage('An unexpected error occurred. Please try again.');
        } finally {
            processingPayment = false;
        }
    }
    
    async function checkPaymentStatus() {
        if (!user) return;
        
        processingPayment = true;
        
        try {
            // TODO: Implement payment status check
            console.log('Payment status check to be implemented');
        } catch (error) {
            console.error('Error checking payment status:', error);
        } finally {
            processingPayment = false;
        }
    }
    
    async function restoreSubscription() {
        if (!user) return;
        
        checkingSubscription = true;
        clearPaymentMessage();
        
        try {
            // Check if user has an active subscription
            const hasActiveSubscription = await checkSubscriptionStatus(user.id);
            
            if (hasActiveSubscription) {
                setPaymentMessage('✅ Active subscription found! Redirecting to app...');
                // // Wait a moment for user to see the message
                // setTimeout(() => {
                //     goto('/app');
                // }, 2000);
            } else {
                setPaymentMessage('No active subscription found. Please select a plan to get started.');
            }
        } catch (error) {
            console.error('Error checking subscription:', error);
            setPaymentMessage('Unable to check subscription status. Please try again or contact support.');
        } finally {
            checkingSubscription = false;
        }
    }
    
    // Check URL parameters for payment status messages
    let paymentMessage = '';
    
    // Use centralized subscription options
    const subscriptionOptions = PRICING_PLANS;
</script>

<svelte:head>
    <title>Pricing - YourBestDays</title>
</svelte:head>

{#if loading}
    <!-- Loading state while checking authentication -->
    <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading...</p>
        </div>
    </div>
{:else}
    <!-- Mobile-first design with proper spacing and touch targets -->
    <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-6 px-4">
        <div class="max-w-4xl mx-auto">
            <!-- Mobile-optimized header -->
            <div class="text-center mb-8">
                <h1 class="text-gray-900 mb-3">Choose Your Plan</h1>
                <p class="text-gray-600">Empower your journey to better days. Select from 3 pricing options:</p>
                
                {#if paymentMessage}
                    <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-3 relative">
                        <button
                            on:click={clearPaymentMessage}
                            class="absolute top-2 right-2 p-1 hover:bg-yellow-100 rounded transition-colors"
                            aria-label="Dismiss message"
                        >
                            <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                        <p class="text-yellow-800 text-sm pr-8">{paymentMessage}</p>
                        {#if paymentMessage.includes('incomplete') || paymentMessage.includes('error') || paymentMessage.includes('check if your payment')}
                            <button
                                on:click={checkPaymentStatus}
                                disabled={processingPayment}
                                class="mt-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-400 text-white text-sm font-medium py-2 px-3 rounded transition-colors"
                            >
                                {#if processingPayment}
                                    Checking...
                                {:else}
                                    Check Payment Status
                                {/if}
                            </button>
                        {/if}
                    </div>
                {/if}
            </div>

            <!-- Custom subscription options -->
            <div class="grid gap-6 md:grid-cols-3">
                {#each subscriptionOptions as option (option.priceId)}
                    <PriceCard
                        priceId={option.priceId}
                        subInterval={option.subInterval}
                        subIntervalType={option.subIntervalType}
                        title={option.name}
                        price={option.price}
                        recommended={option.recommended}
                    >
                        <p class="text-gray-600 text-sm mb-6">{option.description}</p>
                        
                        <ul class="space-y-3 mb-6">
                            {#each option.features as feature (feature)}
                                <li class="flex items-center text-sm text-gray-600">
                                    <svg class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    {feature}
                                </li>
                            {/each}
                        </ul>
                        
                        <button
                            on:click={() => handleSubscriptionSelect(option.priceId)}
                            disabled={processingPayment}
                            class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 {option.recommended ? 'bg-blue-600 hover:bg-blue-700' : ''}"
                        >
                            {#if processingPayment}
                                <div class="flex items-center justify-center">
                                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Processing...
                                </div>
                            {:else}
                                Start Free Trial
                            {/if}
                        </button>
                    </PriceCard>
                {/each}
            </div>
            
            <!-- Payment Instructions -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
                <h3 class="text-sm font-semibold text-blue-900 mb-3">📱 Mobile Payment Instructions</h3>
                <ul class="text text-blue-800 space-y-2">
                    <li>- The app will automatically check your subscription</li>
                    <li>- If you recently purchased a subscription, use the button below to check manually</li>
                </ul>
                
                <div class="mt-4 pt-3 border-t border-blue-200">
                    <button
                        on:click={restoreSubscription}
                        disabled={checkingSubscription}
                        class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                        {#if checkingSubscription}
                            <div class="flex items-center justify-center">
                                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Checking...
                            </div>
                        {:else}
                            🔍 Restore Subscription
                        {/if}
                    </button>
                </div>
            </div>
   
            <!-- Mobile-optimized footer -->
            <div class="text-center mt-8 space-y-4">
                <p class="text-sm text-gray-600">
                    All plans include a 7-day free trial and a 90 day money back guarantee! Cancel anytime.
                </p>
                <button
                    on:click={() => goto('/')}
                    class="text-blue-600 hover:text-blue-500 py-2"
                >
                    ← Back to home
                </button>
            </div>
        </div>
    </div>
{/if}

<!-- Payment Processing Modal -->
<PaymentProcessingModal
    bind:isOpen={showPaymentModal}
/>